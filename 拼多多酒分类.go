package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

const progressFileJiu = "progress_jiu.json"


// 读取上次进度（酒分类）
func loadProgressJiu() int {
	b, err := os.ReadFile(progressFileJiu)
	if err != nil {
		return 0
	}
	var m map[string]int
	if json.Unmarshal(b, &m) == nil {
		return m["last_page"]
	}
	return 0
}

// 保存进度（酒分类）
func saveProgressJiu(page int) {
	_ = os.WriteFile(progressFileJiu, []byte(fmt.Sprintf("{\n  \"last_page\": %d\n}\n", page)), 0644)
}

// ------------------- 全局配置（与原 Python 脚本对应） -------------------

var cookies = map[string]string{
	"api_uid":        "CinRDmiiz0l/MACYanDPAg==",
	"pdd_user_id":    "5356061336423",
	"pdd_user_uin":   "ZIEMO4NFE6JVBDP2LR2U33QPVQ_GEXDA",
	"_nano_fp":       "Xpmyn5P8n5CoXqd8XC_ZBp_taEp24BwbVoxJsbXv",
	"webp":           "1",
	"jrpl":           "7eFB12mP6a75MbrMXgYzL4q9bJ5Iy7AZ",
	"njrpl":          "7eFB12mP6a75MbrMXgYzL4q9bJ5Iy7AZ",
	"dilx":           "AjGndmelCQmdaB835Pytk",
	"PDDAccessToken": "4NF6VK5HIMY3FHEDQVUTVAJNQZDJ2H7FGSJVJ3IGYFJELW5BROLA123676e",
	"pdd_vds":        "gaJLFyzPpaHnXNkGMOYOJtkQkPqGqNZECyYnqQXGCtpNFOZIMohaFtXNrIqm",
}



var baseParams = map[string]string{
	"pdduid":        "6976402261563",
	"page_sn":       "10028",
	"support_types": "0_4",
	"opt_id":        "109",
	"opt_type":      "2",
	"offset":        "0",
	"count":         "20",
	"list_id":       "79wsmy2h5o_109",
	"anti_content":  "0asAfxiydcKgygE2C4edm_0UbX7Pzju-uTzn0hrMJ81welb_uIkcnvIcH8gppPMk3GupDtc8wLF5PN55sLg51uAsgTQPoVgpbKtQ-9NRGev58Zv2QzVPltFL-zBbWWxYWR00ae29leOhuzqR44N4O65X7VQVKd6bgOP3hR07q4EhxWRVM9tWFEpQo9nYI2YQU4B1cgj6xZ6FR5Z0yqV3R5n03xgpigJ6Y506IQipA9m3zVplgN3yTThdzb8Vmhd6a8AKlQn-BWKu0lbV-LVlayMyAgzqZpL8h9v8HhXwIDwUzvyC4yLx-0AKTWGtACwLaEYLngtm2siTfizVVoKPYHT_oMgDHm4J33s6jhoGC1frNsvq5VOht3pzNhqepcU-FU9XLxNFA_Brg0zGHneaNJDuIbztGU-yKEI8dBIkmWPFrw--gM6kAvPouv8JG_eLR4al-oloo5jK0RORglDCfHK0a7iIcTKvlx7kgWjtrpMeilh2aez6ZCWlGHVrGxb4qAKTvS9oXYG6XRGqZ1ls5vA-Z_lFYUml3HwvqSW0xMALvz7rtSWZT-rx4qv0sEXEtEcwuAxuizEe0C0bT2Axi4BssY6jtvVlxnqS2ffFdqJLDt5P9mg5zSHUomSnIiM_MkwX0B_GuHcptwTcmhg37V1V2h68zHOXtHMo3gFmWOiZr41YMVSFY4Yvpc2kUv53dMv-zMAxaslvHV87fuFUGMVgB3SCILvPcM4bpeA2tx5-mRyXJ1t4rXyxP-Xc0wgwccy1RxeEIi55wsDHRTdQePA00c1ji9mQZUA-u-hjrcIB7jbrgN_DROIEkjh5v1svinlU3DlxSlIEhymn5jJuyrtUaaAkWroibbos9vwaZPS4stTsqkzwOYcJS0X6t1KJ78sdnUmMEVSTCMlU-Qa9ZqW4AvR7sIWnrK8wr8w7OAPzJIqBgoy0QwyhmzREMN-vFFlkZsoatHyLDBPLq_UQElu4DcVwsyizA8O-lR0xJ9tKuC1sZdVAW70rVC",
}



// ------------------- 动态 Header 生成 -------------------

var (
	androidDevices = []string{"Redmi Note 8", "Mi 10", "HUAWEI P40", "OPPO R11", "vivo X27", "OnePlus 8 Pro", "HONOR V30", "Meizu 16th", "Samsung SM-G9730", "Pixel 4 XL"}
	androidVersions = []string{"10", "11", "12", "13"}
	netTypes       = []string{"WIFI", "4G", "5G"}
)

func randHex8() string {
	hex := "0123456789ABCDEF"
	b := make([]byte, 8)
	for i := 0; i < 8; i++ {
		b[i] = hex[rand.Intn(len(hex))]
	}
	return string(b)
}

func randChromeVersion() (string, int) {
	majors := []int{120, 121, 122, 123, 124, 125, 126, 138}
	major := majors[rand.Intn(len(majors))]
	build1 := 3000 + rand.Intn(7000)
	build2 := 50 + rand.Intn(150)
	return fmt.Sprintf("%d.0.%d.%d", major, build1, build2), major
}

func randWechatSDKDate() string {
	daysAgo := rand.Intn(91)
	ts := time.Now().Add(-time.Duration(daysAgo) * 24 * time.Hour)
	return ts.Format("20060102")
}

func randXWebVersion() string {
	major := 135 + rand.Intn(5)
	minor := rand.Intn(10)
	build := 100 + rand.Intn(900)
	return fmt.Sprintf("%d%d%d", major, minor, build)
}

func randWXVersion() string {
	patch := 58 + rand.Intn(9)
	build := 2600 + rand.Intn(600)
	hex := randHex8()
	return fmt.Sprintf("8.0.%d.%d(0x%s)", patch, build, hex)
}

func buildUserAgent() (string, int) {
	chromeVer, chromeMajor := randChromeVersion()
	device := androidDevices[rand.Intn(len(androidDevices))]
	androidVer := androidVersions[rand.Intn(len(androidVersions))]
	xweb := randXWebVersion()
	sdkDate := randWechatSDKDate()
	mwebid := 1000 + rand.Intn(9000)
	wxver := randWXVersion()
	net := netTypes[rand.Intn(len(netTypes))]
	ua := fmt.Sprintf(
		"Mozilla/5.0 (Linux; Android %s; %s Build/RKQ1.201004.002; wv) "+
			"AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 "+
			"Chrome/%s Mobile Safari/537.36 "+
			"XWEB/%s MMWEBSDK/%s MMWEBID/%d "+
			"MicroMessenger/%s WeChat/arm64 Weixin NetType/%s "+
			"Language/zh_CN ABI/arm64",
		androidVer, device, chromeVer, xweb, sdkDate, mwebid, wxver, net,
	)
	return ua, chromeMajor
}

func buildReferer() string {
	xShare := randString(22)
	suffix := randString(10)
	referPageID := fmt.Sprintf("10002_%d_%s", time.Now().UnixMilli(), suffix)
	// opt 固定与 Python 一致
	v := url.Values{}
	v.Set("refer_page_name", "index")
	v.Set("opt_id", "109")
	v.Set("opt_name", "%E4%B8%AD%E5%A4%96%E5%90%8D%E9%85%92")
	v.Set("opt_type", "2")
	v.Set("goods_id", "757482183038")
	v.Set("_x_share_id", xShare)
	v.Set("refer_page_id", referPageID)
	v.Set("refer_page_sn", "10002")
	return "https://mobile.pinduoduo.com/catgoods.html?" + v.Encode()
}

func randString(n int) string {
	alphabet := "abcdefghijklmnopqrstuvwxyz0123456789"
	b := make([]byte, n)
	for i := 0; i < n; i++ {
		b[i] = alphabet[rand.Intn(len(alphabet))]
	}
	return string(b)
}

func generateHeaders() http.Header {
	headers := http.Header{}
	ua, chromeMajor := buildUserAgent()
	headers.Set("Host", "mobile.pinduoduo.com")
	headers.Set("x-requested-with", "com.tencent.mm")
	headers.Set("verifyauthtoken", "W1foe5V-90_-a0EnxeL8Og6b8f5f51816bc51ff")
	headers.Set("user-agent", ua)
	headers.Set("sec-ch-ua-platform", "\"Android\"")
	headers.Set("sec-ch-ua-mobile", "?1")
	headers.Set("sec-ch-ua", fmt.Sprintf("\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"%d\", \"Android WebView\";v=\"%d\"", chromeMajor, chromeMajor))
	headers.Set("accept", "application/json, text/plain, */*")
	headers.Set("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7")
	headers.Set("priority", "u=1, i")
	headers.Set("sec-fetch-site", "same-origin")
	headers.Set("sec-fetch-mode", "cors")
	headers.Set("sec-fetch-dest", "empty")
	headers.Set("referer", buildReferer())
	return headers
}

// 阻塞等待用户在控制台输入新的 PDDAccessToken
func promptNewToken() string {
	reader := bufio.NewReader(os.Stdin)
	for {
		fmt.Print("请输入新的 PDDAccessToken，然后按回车继续采集：\n> ")
		token, _ := reader.ReadString('\n')
		token = strings.TrimSpace(token)
		if token != "" {
			return token
		}
		fmt.Println("输入为空，请重新输入。")
	}
}

// ------------------- 爬虫主体 -------------------

type PinduoduoSpider struct {
	client     *mongo.Client
	collection *mongo.Collection
}

func NewSpider(ctx context.Context) (*PinduoduoSpider, error) {
	// 连接 MongoDB
	clientOpts := options.Client().ApplyURI("**************************************************")
	// 若需要可启用压缩：clientOpts.SetCompressors([]string{"zstd"})
	client, err := mongo.Connect(ctx, clientOpts)
	if err != nil {
		return nil, err
	}
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		_ = client.Disconnect(ctx)
		return nil, err
	}

	db := client.Database("pinduoduo")
	coll := db.Collection("pinduoduo_jiu")

	// 索引：goods_id 唯一；crawl_time；page_num

	_, _ = coll.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "goods_id", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "crawl_time", Value: -1}}},
		{Keys: bson.D{{Key: "page_num", Value: 1}}},
	})

	return &PinduoduoSpider{client: client, collection: coll}, nil
}

func (s *PinduoduoSpider) fetchPageData(ctx context.Context, pageNum int, offset int) (bool, map[string]interface{}) {
	// 复制基础参数
	params := url.Values{}
	for k, v := range baseParams {
		params.Set(k, v)
	}
	params.Set("offset", fmt.Sprintf("%d", offset))

	endpoint := "https://mobile.pinduoduo.com/proxy/api/api/caterham/query/subfenlei_gyl_label"
	u, _ := url.Parse(endpoint)
	u.RawQuery = params.Encode()


	client := &http.Client{Timeout: 15 * time.Second}
	for {
		req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
		if err != nil {
			log.Printf("❌ 第 %d 页创建请求失败: %v", pageNum, err)
			return false, nil
		}

		// Cookies
		var cookiePairs []string
		for k, v := range cookies {
			cookiePairs = append(cookiePairs, fmt.Sprintf("%s=%s", k, v))
		}
		req.Header = generateHeaders()
		req.Header.Set("Cookie", strings.Join(cookiePairs, "; "))

		resp, err := client.Do(req)
		if err != nil {
			log.Printf("❌ 第 %d 页请求异常: %v", pageNum, err)
			// 异常也提示用户更新 token 再试（可能是被封）
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}

		if resp.StatusCode != 200 {
			log.Printf("❌ 第 %d 页请求失败，状态码: %d", pageNum, resp.StatusCode)
			// 仅当403或其他非200时，等待用户更换token
			if resp.Body != nil {
				_ = resp.Body.Close()
			}
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}
		defer resp.Body.Close()

		var data map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
			log.Printf("❌ 第 %d 页JSON解析失败: %v", pageNum, err)
			return false, nil
		}

		// 业务层校验：命中 VERIFY/54001/成功=false 且 status=200 时，等待用户更新 Token 并重试
		shouldPrompt := false
		if v, ok := data["error_code"]; ok {
			if int(getNumber(v)) == 54001 {
				shouldPrompt = true
			}
		}
		if strings.ToUpper(getString(data["judgement"])) == "VERIFY" {
			shouldPrompt = true
		}
		if succ, ok := data["success"].(bool); ok && !succ {
			if int(getNumber(data["status"])) == 200 {
				shouldPrompt = true
			}
		}
		if shouldPrompt {
			if tok := getString(data["verify_auth_token"]); tok != "" {
				log.Printf("⚠️ 接口返回校验状态，verify_auth_token=%s", tok)
			}
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}

		// 打印完整的响应 JSON（美化缩进）
		if pretty, err := json.MarshalIndent(data, "", "  "); err == nil {
			log.Printf("📦 第 %d 页响应:\n%s", pageNum, string(pretty))
		}

		log.Printf("✅ 第 %d 页获取成功，offset: %d", pageNum, offset)
		return true, data
	}
}


func (s *PinduoduoSpider) processAndSaveData(ctx context.Context, goodsList []interface{}, pageNum int) int {
	successCount := 0
	now := time.Now()

	for _, it := range goodsList {
		goods, ok := it.(map[string]interface{})
		if !ok {
			continue
		}
		goodsID := getString(goods["goods_id"]) // 必须
		if goodsID == "" {
			continue
		}

		doc := bson.M{
			"goods_id":                 goodsID,
			"goods_name":               getString(goods["goods_name"]),
			"price":                    getNumber(goods["price"]),
			"price_info":               getString(goods["price_info"]),
			"sales":                    getNumber(goods["sales"]),
			"sales_tip":                getString(goods["sales_tip"]),
			"image_url":                getString(goods["image_url"]),
			"link_url":                 getString(goods["link_url"]),
			"market_price":             getNumber(goods["market_price"]),
			"normal_price":             getNumber(goods["normal_price"]),
			"min_on_sale_group_price":  getNumber(goods["min_on_sale_group_price"]),
			"customer_num":             getNumber(goods["customer_num"]),
			"quality":                  getNumber(goods["quality"]),
			"goods_type":               getNumber(goods["goods_type"]),
			"activity_type":            getNumber(goods["activity_type"]),
			"event_type":               getNumber(goods["event_type"]),
			"crawl_time":               now,
			"page_num":                 pageNum,
			"server_time":              getNumber(goods["server_time"]),
			"raw_data":                 goods,
		}

		filter := bson.M{"goods_id": goodsID}
		update := bson.M{"$set": doc}
		res, err := s.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
		if err != nil {
			log.Printf("     商品 %s 保存失败: %v", goodsID, err)
			continue
		}
		if res.UpsertedID != nil || res.ModifiedCount > 0 || res.MatchedCount > 0 {
			successCount++
		}
	}
	return successCount
}

func (s *PinduoduoSpider) run(ctx context.Context, totalPages int) {
	countPerPage := 20
	successfulPages := 0
	failedPages := 0
	totalGoods := 0

	log.Println("🚀 开始爬取拼多多商品数据并保存到数据库...")

	start := loadProgressJiu()+1
	if start < 1 { start = 1 }
	for pageNum := start; pageNum <= totalPages; pageNum++ {
		offset := (pageNum - 1) * countPerPage
		log.Printf("\n正在获取第 %d 页数据...", pageNum)

		success, data := s.fetchPageData(ctx, pageNum, offset)
		if success {
			successfulPages++
			if data != nil {
				if gl, ok := data["goods_list"].([]interface{}); ok {
					cnt := s.processAndSaveData(ctx, gl, pageNum)
					// 成功处理后保存进度（酒分类）
					saveProgressJiu(pageNum)
					totalGoods += cnt
				}
			}
		} else {
			failedPages++
		}

		// 每20页暂停20分钟
		if pageNum%20 == 0 && pageNum < totalPages {
			log.Printf("\n🛑 已完成 %d 页，暂停 20 分钟休息...", pageNum)

			log.Printf("⏰ 暂停开始时间: %s", time.Now().Format("2006-01-02 15:04:05"))

			// 20分钟 = 1200秒
			restDuration := 20 * time.Minute
			time.Sleep(restDuration)

			log.Printf("🚀 休息结束，继续采集... 当前时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		} else if pageNum < totalPages {
			// 正常页间延时
			delay := 15 + rand.Float64()*10 // 15~20秒
			log.Printf("⏳ 等待 %.1f 秒后继续...", delay)
			time.Sleep(time.Duration(delay * float64(time.Second)))
		}

		// 每10页显示进度（保持原有逻辑）
		if pageNum%10 == 0 {
			log.Printf("\n📊 进度: %d/%d页", pageNum, totalPages)
			log.Printf("✅ 成功: %d页, ❌ 失败: %d页", successfulPages, failedPages)
			log.Printf("📦 总商品数(本次新/更): %d", totalGoods)
			log.Println(strings.Repeat("-", 50))
		}
	}

	log.Println("\n🎉 爬取完成!")
	log.Printf("总页数: %d", totalPages)
	log.Printf("成功页数: %d", successfulPages)
	log.Printf("失败页数: %d", failedPages)
	log.Printf("总商品数(本次新/更): %d", totalGoods)
}

func (s *PinduoduoSpider) getStats(ctx context.Context) {
	total, err := s.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		log.Printf("统计失败: %v", err)
		return
	}
	var latest bson.M
	err = s.collection.FindOne(ctx, bson.M{}, options.FindOne().SetSort(bson.D{{Key: "crawl_time", Value: -1}})).Decode(&latest)
	log.Printf("\n📊 数据库统计:")
	log.Printf("总记录数: %d", total)
	if err == nil {
		log.Printf("最新采集时间: %v", latest["crawl_time"])
		log.Printf("最新商品ID: %v", latest["goods_id"])
	}
}

// ------------------- 工具函数 -------------------

func getString(v interface{}) string {
	switch t := v.(type) {
	case string:
		return t
	case json.Number:
		return t.String()
	case float64:
		return fmt.Sprintf("%v", t)
	case int64:
		return fmt.Sprintf("%d", t)
	default:
		return ""
	}
}

func getNumber(v interface{}) float64 {
	switch t := v.(type) {
	case float64:
		return t
	case int:
		return float64(t)
	case int64:
		return float64(t)
	case json.Number:
		f, _ := t.Float64()
		return f
	case string:
		// 尝试解析
		if f, err := strconvParseFloat(t); err == nil {
			return f
		}
	}
	return 0
}

func strconvParseFloat(s string) (float64, error) {
	return strconv.ParseFloat(s, 64)
}

// ------------------- 入口 -------------------

func main() {
	rand.Seed(time.Now().UnixNano())
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	spider, err := NewSpider(ctx)
	if err != nil {
		log.Fatalf("创建爬虫失败: %v", err)
	}
	defer func() {
		_ = spider.client.Disconnect(context.Background())
	}()

	// 跟原脚本一致：默认抓取 600 页
	spider.run(context.Background(), 100)
	spider.getStats(context.Background())
}

