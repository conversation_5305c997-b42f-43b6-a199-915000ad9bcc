import time
import mitmproxy.http
import pymongo
from mitmproxy import http
import re
import json
import datetime
import redis

class CaptureInfoWriteFile:
    def __init__(self):
        # 连接MongoDB数据库
        self.client = pymongo.MongoClient(
            "**************************************************", 
            compressors='zstd'
        )
        # 创建抖音抓包数据库和数据表
        self.db = self.client["pinduoduo"]
        self.collection = self.db["pinduoduo_jiu"]
        print("初始化完成，开始抓包...")

    def request(self, flow: mitmproxy.http.HTTPFlow):
        # 处理mtop相关请求
        if 'mtop' in flow.request.url:
            flow.request.headers['Accept-Encoding'] = 'gzip'

    def response(self, flow: mitmproxy.http.HTTPFlow):
        # 处理商品页面响应
        # 店铺列表：https://mobile.yangkeduo.com/proxy/api/api/search/mall/plus?pdduid
        # 分类列表：https://mobile.yangkeduo.com/proxy/api/api/caterham/query/subfenlei_gyl_label?pdduid=
        # 搜索列表：https://mobile.yangkeduo.com/proxy/api/search?pdduid=
        if ('proxy/api/api/search/opt/10048/groups?pdduid' in flow.request.url and 
            '{"header_list":{"headers":' in str(flow.response.text)):
            # 检查账号状态
            if '商品已售罄，推荐以下相似商品' in str(flow.response.text):
                print('账号可能已被限制，建议休息')
                return
            if 'window.rawData=null' in str(flow.response.text):
                print('检测到 window.rawData=null，可能是封号或无数据，跳过存储')
                return
            try:
                # 提取商品ID
                goods_id_match = re.search(r'"goodsID":(\d+),"brandId":', str(flow.response.text))
                if goods_id_match:
                    goods_id = goods_id_match.group(1)
                    print(f"提取到商品ID: {goods_id}")
                else:
                    print("未找到商品ID")
                    goods_id = None

                # 获取当前时间（包含小时和分钟）
                capture_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
                
                # 准备要存储的数据
                data = {
                    "url":flow.request.url,
                    # "goods_id": goods_id,
                    "capture_time": capture_time,
                    "response_text": str(flow.response.text),  # 限制文本长度，避免过大
                }
                
                # 写入数据库
                result = self.collection.insert_one(data)
                print(f"数据已写入，ID: {result.inserted_id}, 时间: {capture_time}")
                
            except Exception as e:
                print(f"处理响应时出错: {str(e)}")

addons = [CaptureInfoWriteFile()]

