#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拼多多酒分类接口的修改
"""

import requests
import json

# 从修改后的文件导入配置
from 拼多多酒分类 import cookies, generate_headers, base_search_params, <PERSON>nd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

def test_api_call(offset=0):
    """
    测试API调用
    """
    print(f"\n🧪 测试 offset={offset} 的API调用...")
    
    # 创建爬虫实例来使用其方法
    spider = PinduoduoSpider()
    
    # 准备参数
    params = base_search_params.copy()
    params = spider.update_pagination_params(params, offset)
    
    # 准备headers
    headers = generate_headers(offset)
    
    print(f"📋 请求参数:")
    print(f"   offset: {params['offset']}")
    print(f"   flip: {params['flip']}")
    print(f"   anti_content: {params['anti_content'][:50]}...")
    print(f"   referer: {headers['referer'][:100]}...")
    
    try:
        # 发送请求
        response = requests.get(
            'https://mobile.yangkeduo.com/proxy/api/api/search/opt/10048/groups',
            params=params,
            cookies=cookies,
            headers=headers,
            timeout=15
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                
                # 提取商品列表
                goods_list = spider.extract_goods_list(data)
                print(f"✅ 成功获取 {len(goods_list)} 个商品")
                
                # 显示前3个商品的基本信息
                for i, goods in enumerate(goods_list[:3]):
                    print(f"   商品{i+1}: {goods.get('goods_name', 'N/A')[:30]} - 价格: {goods.get('price', 'N/A')}")
                
                return True, len(goods_list)
                
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return False, 0
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            return False, 0
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False, 0
    except Exception as e:
        print(f"❌ 其他异常: {e}")
        return False, 0
    finally:
        # 关闭数据库连接
        if hasattr(spider, 'client'):
            spider.client.close()

def main():
    """
    主测试函数
    """
    print("🚀 开始测试拼多多酒分类接口修改...")
    
    # 测试不同的offset值
    test_offsets = [0, 20, 40]
    results = []
    
    for offset in test_offsets:
        success, count = test_api_call(offset)
        results.append((offset, success, count))
    
    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print("-" * 50)
    for offset, success, count in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"offset={offset:2d}: {status} - 商品数: {count}")
    
    # 统计
    success_count = sum(1 for _, success, _ in results if success)
    print(f"\n🎯 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count > 0:
        print("✅ 接口修改成功！可以正常获取数据")
    else:
        print("❌ 接口修改可能有问题，请检查参数配置")

if __name__ == "__main__":
    main()
