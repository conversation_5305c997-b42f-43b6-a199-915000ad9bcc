#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
拼多多商品数据解析器
从test.txt中解析商品信息并逐条存入数据库（限制20条）
"""

import json
import pymongo
import datetime
from typing import List, Dict, Any
import re

class PinduoduoDataParser:
    def __init__(self):
        """初始化数据库连接"""
        try:
            # 连接MongoDB数据库（使用与原爬虫相同的连接）
            self.client = pymongo.MongoClient(
                "**************************************************", 
                compressors='zstd'
            )
            # 使用与原爬虫相同的集合存储商品数据
            self.db = self.client["pinduoduo"]
            self.collection = self.db["pinduoduo_jiu"]
            print("✅ 数据库连接成功")
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            raise

    def parse_json_response(self, file_path: str) -> Dict[str, Any]:
        """解析JSON响应文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 成功读取JSON文件: {file_path}")
            return data
        except Exception as e:
            print(f"❌ 解析JSON文件失败: {e}")
            raise

    def extract_goods_info(self, goods_item: Dict[str, Any]) -> Dict[str, Any]:
        """提取单个商品的核心信息"""
        try:
            # 提取核心商品信息
            goods_info = {
                # 商品基础信息
                "goods_id": goods_item.get("goods_id"),
                "goods_name": goods_item.get("goods_name", "").strip(),
                "short_goods_name": goods_item.get("short_goods_name", "").strip(),
                
                # 价格信息（转换为元）
                "price": round(goods_item.get("price", 0) / 100, 2),  # 分转元
                "market_price": round(goods_item.get("market_price", 0) / 100, 2),
                "normal_price": round(goods_item.get("normal_price", 0) / 100, 2),
                "coupon_price": round(goods_item.get("couponPromoPrice", 0) / 100, 2),
                
                # 商品统计信息
                "sales": goods_item.get("sales", 0),
                "sales_tip": goods_item.get("sales_tip", ""),
                
                # 商品图片
                "thumb_url": goods_item.get("thumb_url", ""),
                "hd_thumb_url": goods_item.get("hd_thumb_url", ""),
                "image_url": goods_item.get("image_url", ""),
                
                # 店铺和品牌信息
                "mall_id": goods_item.get("mall_id"),
                "brand_id": goods_item.get("brand_id"),
                "merchant_type": goods_item.get("merchant_type", 0),
                
                # 活动和标签信息
                "activity_type": goods_item.get("activity_type", 0),
                "tag_list": self._extract_tags(goods_item.get("tag_list", [])),
                
                # 链接信息
                "link_url": goods_item.get("link_url", ""),
                
                # 其他信息
                "quality": goods_item.get("quality", 0),
                "list_type": goods_item.get("list_type", 0),
                "price_type": goods_item.get("price_type", 0),
                
                # 广告信息
                "is_ad": 1 if goods_item.get("ad") else 0,
                "ad_id": goods_item.get("adid", 0),
                
                # 解析时间
                "parse_time": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "parse_date": datetime.datetime.now().strftime("%Y-%m-%d"),
                "crawl_time": datetime.datetime.now(),

                # 原始数据（保留完整的商品数据以备查看）
                "raw_goods_data": goods_item
            }
            
            return goods_info
            
        except Exception as e:
            print(f"❌ 提取商品信息失败: {e}")
            return {}

    def _extract_tags(self, tag_list: List[Dict]) -> List[str]:
        """提取标签文本"""
        tags = []
        for tag in tag_list:
            if isinstance(tag, dict) and "text" in tag:
                tags.append(tag["text"])
        return tags

    def save_goods_to_db(self, goods_info: Dict[str, Any]) -> bool:
        """保存单个商品到数据库"""
        try:
            # 检查商品是否已存在（基于goods_id）
            existing = self.collection.find_one({"goods_id": goods_info["goods_id"]})
            
            if existing:
                # 更新现有记录
                result = self.collection.update_one(
                    {"goods_id": goods_info["goods_id"]},
                    {"$set": goods_info}
                )
                print(f"📝 更新商品: {goods_info['goods_name'][:30]}... (ID: {goods_info['goods_id']})")
                return result.modified_count > 0
            else:
                # 插入新记录
                result = self.collection.insert_one(goods_info)
                print(f"➕ 新增商品: {goods_info['goods_name'][:30]}... (ID: {goods_info['goods_id']})")
                return result.inserted_id is not None
                
        except Exception as e:
            print(f"❌ 保存商品失败: {e}")
            return False

    def parse_and_save_all(self, file_path: str) -> Dict[str, int]:
        """解析所有商品数据并逐条保存到数据库"""
        print("🚀 开始解析拼多多商品数据...")
        
        # 解析JSON文件
        data = self.parse_json_response(file_path)
        
        # 获取商品列表，限制为前20条
        goods_list = data.get("goods_list", [])[:20]
        total_goods = len(goods_list)

        print(f"📊 找到商品数据，限制处理前 {total_goods} 个商品，开始逐条解析和存储...")
        
        # 统计信息
        stats = {
            "total": total_goods,
            "success": 0,
            "failed": 0,
            "updated": 0,
            "inserted": 0
        }
        
        # 逐条处理商品
        for i, goods_item in enumerate(goods_list, 1):
            print(f"\n📦 处理第 {i}/{total_goods} 个商品...")
            
            # 提取商品信息
            goods_info = self.extract_goods_info(goods_item)
            
            if not goods_info:
                print(f"⚠️  商品 {i} 信息提取失败")
                stats["failed"] += 1
                continue
            
            # 保存到数据库
            if self.save_goods_to_db(goods_info):
                stats["success"] += 1
                # 检查是更新还是插入
                existing = self.collection.find_one({"goods_id": goods_info["goods_id"]})
                if existing and existing.get("parse_time") != goods_info["parse_time"]:
                    stats["updated"] += 1
                else:
                    stats["inserted"] += 1
            else:
                stats["failed"] += 1
            
            # 显示进度
            if i % 5 == 0 or i == total_goods:
                print(f"📈 进度: {i}/{total_goods} ({i/total_goods*100:.1f}%)")
        
        return stats

    def print_summary(self, stats: Dict[str, int]):
        """打印处理结果汇总"""
        print("\n" + "="*50)
        print("📊 处理结果汇总")
        print("="*50)
        print(f"总商品数量: {stats['total']}")
        print(f"成功处理: {stats['success']}")
        print(f"处理失败: {stats['failed']}")
        print(f"新增商品: {stats['inserted']}")
        print(f"更新商品: {stats['updated']}")
        print(f"成功率: {stats['success']/stats['total']*100:.1f}%")
        print("="*50)

    def close_connection(self):
        """关闭数据库连接"""
        if hasattr(self, 'client'):
            self.client.close()
            print("✅ 数据库连接已关闭")

def main():
    """主函数"""
    parser = None
    try:
        # 创建解析器实例
        parser = PinduoduoDataParser()
        
        # 设置文件路径
        file_path = "test.txt"
        
        # 解析和保存数据
        stats = parser.parse_and_save_all(file_path)
        
        # 打印结果汇总
        parser.print_summary(stats)
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
    
    finally:
        # 确保关闭数据库连接
        if parser:
            parser.close_connection()

if __name__ == "__main__":
    main()