import requests
import re
import urllib.parse
import html
cookies = {
    'HSID': 'AFlrrGkkpQdeZuVdw',
    'SSID': 'AJRzpLdGuv_XfSz2e',
    'APISID': 'eqIWdvXh7WFCzQYT/Ajm-IKO35uK80dJa3',
    'SAPISID': 'OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU',
    '__Secure-1PAPISID': 'OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU',
    '__Secure-3PAPISID': 'OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU',
    'SEARCH_SAMESITE': 'CgQIzJ4B',
    'SID': 'g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LrIhmGQQYHqYXWK5zSUUuvwACgYKAWUSARQSFQHGX2MiRFIcMODOC9WrT-cRKakN3BoVAUF8yKqpyqZEMMj2sxQn91H0Pr3r0076',
    '__Secure-1PSID': 'g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LYtP1X8BfQPGpGOI3f-o-QwACgYKAfYSARQSFQHGX2MiiS90o5G_os9L_38FKqK-tRoVAUF8yKq7K95zMbah8qkVpTjUSmRE0076',
    '__Secure-3PSID': 'g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LGoE5Dzl2xqHQJpAtcXyYFgACgYKAVASARQSFQHGX2MiPuwHyURuCGPs83NO6Z3aphoVAUF8yKqyMFUhsI7AhCRIeOrvfGA20076',
    'AEC': 'AVh_V2hlOePv9OCULWix5pvuK7YFJwK7OMzLjLSD9bR55a54ILuMQs8dug',
    'NID': '525=T2HHUReB6cA6ONjgRU9ERL7tpnjZ6_xFeqmSz2qXtrItvb8w8pF7eOqascaIu-_J7l2RxYtF3YYVhD1ZXjvqZeKC9_r3zyqpbgYB-jjs_YEH6a24Fual3jlCkOu8JimvoT_rD2Y0pmOyCb6yhgHB_w32qHwQqut7LtVahAkhPF4y7GsyiDukhn6gV-XX5TiRJyLMZ2kCw_eebOpc0sxomlB1Ao491Ob1c8UmZjgi98a7WX9JE8JvfXu8SWpu9QIBliNhOrrSUpf5dmcbnusieI_20-PQySxwrmCOCUYXbQTETdoWc5nLq0-g0RGy6SQr814AY3wYFcql-ouavfGSqCp7_FAeRYtL06AypIMuj-FbxB3I4xrq8Gr52BWIJ9peV-3k9fyJw9-KTDb7Zd-6k-94KLvZRMeEBDIyej_LUIaLhNfAt-oHfAkp00RGkNh0fklnQOMIde_fawpcyEtdyYqr8STgGDPaOzErt_PTxSS_ymXK30D1kPyD-DvCMHIPNiKNPXGb2Xx_TtoO6xJRI2Sowozm8iVwzZKzGX91U875eU0feEoZ-pzn1aU4rAjR4oJIO_HrTnbWYAowKAKxtbvoP04mFmcjXPvRMb7REOB-EdDmys60wVA8Q4LatwlQbPLhd8UkfbjlptcmaQpsHWt_PfFMklZ4jr6jmddknJhlMLElI1RwzqyykNuob0aUrhsOynEUsmC7xurQ-86Fl2-Hw0Et3KYhKe3MnQaaKT8',
    '__Secure-1PSIDTS': 'sidts-CjEB5H03P2veuOddagpkVtHE7aQ6Kmf8x0oNxcVy-oGrOuiFN78-8hdRELT-1GuKjkFYEAA',
    '__Secure-3PSIDTS': 'sidts-CjEB5H03P2veuOddagpkVtHE7aQ6Kmf8x0oNxcVy-oGrOuiFN78-8hdRELT-1GuKjkFYEAA',
    'DV': 'o14YV3OIywRusLemEEJzl1lg0QRWjtn1BMNwPWIBWwAAAPADV2lI7DiWVwAAAExP9_4OWcuxKgAAAHP6707ElJM3CwAAQImvEEkdt3wgBAAAAA',
    'SIDCC': 'AKEyXzXZSVL59vbFhuaSzd6_OkT5Lc3dLJ0W-e93IhogyB1XIQ4DfMUHqqlbexpvzt4M7bu6Z5Y',
    '__Secure-1PSIDCC': 'AKEyXzXUfnxxEi4a0yhTIjOV-wxCWM8Is_afGenR1LtPwjJ-_ixsdIgWaMvtGPIqQCDS0kHdvrY',
    '__Secure-3PSIDCC': 'AKEyXzU39B0a8mHXAjPtLdiOc5fGlNDIWTOeq6cEvDgj2NoNnfLbZfshr5g7wiLlcJnvXFnZp5A',
}

headers = {
    'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
    'downlink': '1.35',
    'priority': 'u=0, i',
    'rtt': '400',
    'sec-ch-prefers-color-scheme': 'light',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Chromium";v="139", "Google Chrome";v="139"',
    'sec-ch-ua-arch': '"x86"',
    'sec-ch-ua-bitness': '"64"',
    'sec-ch-ua-form-factors': '"Desktop"',
    'sec-ch-ua-full-version': '"139.0.7258.67"',
    'sec-ch-ua-full-version-list': '"Not;A=Brand";v="99.0.0.0", "Chromium";v="139.0.7258.67", "Google Chrome";v="139.0.7258.67"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-model': '""',
    'sec-ch-ua-platform': '"Windows"',
    'sec-ch-ua-platform-version': '"10.0.0"',
    'sec-ch-ua-wow64': '?0',
    'sec-fetch-dest': 'document',
    'sec-fetch-mode': 'navigate',
    'sec-fetch-site': 'none',
    'sec-fetch-user': '?1',
    'upgrade-insecure-requests': '1',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    # 'cookie': 'HSID=AFlrrGkkpQdeZuVdw; SSID=AJRzpLdGuv_XfSz2e; APISID=eqIWdvXh7WFCzQYT/Ajm-IKO35uK80dJa3; SAPISID=OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU; __Secure-1PAPISID=OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU; __Secure-3PAPISID=OqvNMnXnD0AEfzTC/AZUE6sYXlF7YXWMmU; SEARCH_SAMESITE=CgQIzJ4B; SID=g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LrIhmGQQYHqYXWK5zSUUuvwACgYKAWUSARQSFQHGX2MiRFIcMODOC9WrT-cRKakN3BoVAUF8yKqpyqZEMMj2sxQn91H0Pr3r0076; __Secure-1PSID=g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LYtP1X8BfQPGpGOI3f-o-QwACgYKAfYSARQSFQHGX2MiiS90o5G_os9L_38FKqK-tRoVAUF8yKq7K95zMbah8qkVpTjUSmRE0076; __Secure-3PSID=g.a0000Qh7ejyy7yrEUns1d9qZ0wGUwUWU_kGZ7JK0OEep6o-sik6LGoE5Dzl2xqHQJpAtcXyYFgACgYKAVASARQSFQHGX2MiPuwHyURuCGPs83NO6Z3aphoVAUF8yKqyMFUhsI7AhCRIeOrvfGA20076; AEC=AVh_V2hlOePv9OCULWix5pvuK7YFJwK7OMzLjLSD9bR55a54ILuMQs8dug; NID=525=T2HHUReB6cA6ONjgRU9ERL7tpnjZ6_xFeqmSz2qXtrItvb8w8pF7eOqascaIu-_J7l2RxYtF3YYVhD1ZXjvqZeKC9_r3zyqpbgYB-jjs_YEH6a24Fual3jlCkOu8JimvoT_rD2Y0pmOyCb6yhgHB_w32qHwQqut7LtVahAkhPF4y7GsyiDukhn6gV-XX5TiRJyLMZ2kCw_eebOpc0sxomlB1Ao491Ob1c8UmZjgi98a7WX9JE8JvfXu8SWpu9QIBliNhOrrSUpf5dmcbnusieI_20-PQySxwrmCOCUYXbQTETdoWc5nLq0-g0RGy6SQr814AY3wYFcql-ouavfGSqCp7_FAeRYtL06AypIMuj-FbxB3I4xrq8Gr52BWIJ9peV-3k9fyJw9-KTDb7Zd-6k-94KLvZRMeEBDIyej_LUIaLhNfAt-oHfAkp00RGkNh0fklnQOMIde_fawpcyEtdyYqr8STgGDPaOzErt_PTxSS_ymXK30D1kPyD-DvCMHIPNiKNPXGb2Xx_TtoO6xJRI2Sowozm8iVwzZKzGX91U875eU0feEoZ-pzn1aU4rAjR4oJIO_HrTnbWYAowKAKxtbvoP04mFmcjXPvRMb7REOB-EdDmys60wVA8Q4LatwlQbPLhd8UkfbjlptcmaQpsHWt_PfFMklZ4jr6jmddknJhlMLElI1RwzqyykNuob0aUrhsOynEUsmC7xurQ-86Fl2-Hw0Et3KYhKe3MnQaaKT8; __Secure-1PSIDTS=sidts-CjEB5H03P2veuOddagpkVtHE7aQ6Kmf8x0oNxcVy-oGrOuiFN78-8hdRELT-1GuKjkFYEAA; __Secure-3PSIDTS=sidts-CjEB5H03P2veuOddagpkVtHE7aQ6Kmf8x0oNxcVy-oGrOuiFN78-8hdRELT-1GuKjkFYEAA; DV=o14YV3OIywRusLemEEJzl1lg0QRWjtn1BMNwPWIBWwAAAPADV2lI7DiWVwAAAExP9_4OWcuxKgAAAHP6707ElJM3CwAAQImvEEkdt3wgBAAAAA; SIDCC=AKEyXzXZSVL59vbFhuaSzd6_OkT5Lc3dLJ0W-e93IhogyB1XIQ4DfMUHqqlbexpvzt4M7bu6Z5Y; __Secure-1PSIDCC=AKEyXzXUfnxxEi4a0yhTIjOV-wxCWM8Is_afGenR1LtPwjJ-_ixsdIgWaMvtGPIqQCDS0kHdvrY; __Secure-3PSIDCC=AKEyXzU39B0a8mHXAjPtLdiOc5fGlNDIWTOeq6cEvDgj2NoNnfLbZfshr5g7wiLlcJnvXFnZp5A',
}
n = 0
for i in range(1,20):
    n +=10
    params = {
        "q": "赠送VIP体验券特权",
        "sca_esv": "7fb9df9863b39f3b",
        "sxsrf": "AE3TifMDyYlDLPG2FsWp2mL8L1zpAroPeg:1756195575929",
        "ei": "92qtaPu7OLek5NoP5b606Ag",
        "start": n,
        "sa": "N",
        "sstk": "Ac65TH4aj0Ab29mInUBmEIViAeSRKAIt1n-2EHGmNgFkWsZchYKu0ca4lbkhzpyb_05HkembRvdvTGV90JA1dTBc8ItzHeT3G1XHgg",
        "ved": "2ahUKEwi7k9argqiPAxU3ElkFHWUfDY0Q8tMDegQICBAE",
        "cshid": "1756195593045247",
        "biw": "1528",
        "bih": "746",
        "dpr": "1.25"
    }

    response = requests.get('https://www.google.com/search', params=params, cookies=cookies, headers=headers,proxies={'http':'http://127.0.0.1:7890','https':'http://127.0.0.1:7890'})

    response.encoding = 'utf-8'
    response = response.text
    # print(response)
    with open('test.txt','w') as f:
        f.write(response)

    with open("test.txt", "r", encoding="utf-8") as f:
        html_text = f.read()

    # 正则匹配
    pattern = re.compile(r"https://y\.qq\.com/m/vip/experience_card/dist/index\.html\?[^\"'\s<]+")

    matches = pattern.findall(html_text)

    # 去重 + 反转义
    clean_links = set()
    for m in matches:
        # HTML 实体转义（把 &amp; 转回 &）
        m = html.unescape(m)
        # Unicode 转义（把 \u003d 转成 =）
        m = m.encode('utf-8').decode('unicode_escape')
        # URL 解码（把 %3D 转成 =）
        m = urllib.parse.unquote(m)
        clean_links.add(m)

    print("最终唯一链接：")
    for link in clean_links:
        print(link)