package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

const progressFileBaijiu = "progress_baijiu.json"

// 读取上次进度（白酒分类）
func loadProgressBaijiu() int {
	b, err := os.ReadFile(progressFileBaijiu)
	if err != nil {
		return 0
	}
	var m map[string]int
	if json.Unmarshal(b, &m) == nil {
		return m["last_page"]
	}
	return 0
}

// 保存进度（白酒分类）
func saveProgressBaijiu(page int) {
	_ = os.WriteFile(progressFileBaijiu, []byte(fmt.Sprintf("{\n  \"last_page\": %d\n}\n", page)), 0644)
}

// ------------------- 全局配置（更新为白酒分类） -------------------

var cookies = map[string]string{
	"api_uid":        "CiUQCWiutjYcTgCJjSaUAg==",
	"PDDAccessToken": "ABKC3GLVU3RDYY3GEZQODQOARYQ3EPV3W5B5VP2H2O5LNLKFS3QA121249a",
	"pdd_user_id":    "2106797737252",
	"pdd_user_uin":   "37F6E632ZIXRIPCFEGRT6ESW3Q_GEXDA",
	"_nano_fp":       "Xpmyn5PJX0X8n0TynT_2eJDAkSuIRFRUfog_r_m3",
	"webp":           "1",
	"njrpl":          "MHbNzvylaDKDXECtf2wyrqd3SdxcKIqa",
	"jrpl":           "MHbNzvylaDKDXECtf2wyrqd3SdxcKIqa",
	"dilx":           "L~F9SxxIqg28h5bHQsKhX",
	"pdd_vds":        "gagLKykPzPZIhbZtVbvQzIXnJyZGVNgNMoJokPWyXQJPHovEqngLJLJGVnHb",
}

var baseParams = map[string]string{
	"pdduid":                   "2106797737252",
	"source":                   "search",
	"opt_type":                 "3",
	"white_ground_pic_goods_id": "0",
	"refer_tab":                "[999999, 10048, 10032, 20759, 20758, 20757, 10757]",
	"opt1_id":                  "9975",
	"opt2_id":                  "999999",
	"opt_name":                 "白酒",
	"_x_link_id":               "c702554fa85a94c0ca877d9f8b19ddb9",
	"flip":                     "0;0;0;0;196f2b6c-2222-3300-7f4d-0b16eb37e959;/20;0;0;26e8dd4f40dee1f3f2a61f2cafe50b53",
	"count":                    "20",
	"list_id":                  "tcb1mhymbf",
	"sort_type":                "default",
	"filter":                   "",
	"tab_enable":               "1",
	"anti_content":             "0asWfxiF-cKjsVexNif8Qv-ZW8mO423FZeLqdFq_7mO0cIlodYMaH_AActRZBwLMFFSFYlA__RkCkSArKXJvnpsG175pqP5R4_t0YbKtgUwPS5UmGOoQSZ0vg_xG1cdCCvNu6rNoRizbX0zKq99c6jr3xtN2wi8V5vbBJrO2XoaQ-qtVGna6vLPVQQaGma79kCZeW1l2nqg3L5j46V9mj4oPh9Chg1cwO2phgbAeugGRfxDkOsmRPLLxPPQN2njST7mOg7oOh9YEt9J7Q2CV0WZR1nRGbsJN92iT9eob76L-9YRZuC1IYfeZqCKlYfeVqC1sq6RKUA-N9_WK1uvN0U9xw4GisysQPfzyxOZ-hUE4SVebQAeVnQyZjraqNHQmi_scUz_48y6yrg7G6cWHmBmbLHgc2JXRKkHvC0g0BsG1XHvO7nbi5Y7GdPMBMYkIY1rAGjWyj8U5ZXnmnwGkmjFWscUJpQXEDpMLCkgVFuKBVOs6L1bEfs4PEawbAFq7V-RsYZywcq8czEp1wN6AoPxfeeFFwuFE-TF7eSaa_ygHDVe5No3-IFiuLn60Az81JReEbskyHTAxR9FsU6cwAhAP6Xnf-AVWX_5G5kKqnghk-LlAU0AckI05iZX8xbS5TKHqFxrWI4TYETqU-J2p0NhvdV6FqYE_wvYo44Yt4xM8fVyLoDJfYSOsCaD3KI1oZ_pcdcjFO7n2SecElWda4NV--A5C0hD-x7gRtUeyNsNzvew-NIBsy3gsgAxaWxVcBfo0xWeWPS1u8kaIj8HvEjEqIODyMPhtoZhTwWahIRrXUqpBhKmB1FF--rEIRGRy0SMj91pBHtPlLJnDa7i1JiLIfFwZBKy8p87Fb1DY3T3Aaxini8_eYTDiITA2BVpAqoAxWy4U8T-H555PFU4MwjfW8Ta7mwSotMxdOstI-Q0xF5J-d5uZ3CZbHlMNnhvEqJ0qIpi_HLk6yRvYEsojfaAmteyvzE6RyAYv53GvsGfsJjcdXyQFnSWDACtI2MjCiTAXlNs_r12dCN0ify-X3eJ1Q9z",
	"opt_source":               "search_opt_goods",
}

// ------------------- 动态 Header 生成 -------------------

func buildUserAgent() string {
	return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309092b) XWEB/14315 Flue"
}

func buildReferer() string {
	return "https://mobile.yangkeduo.com/relative_goods.html?__rp_name=search_catgoods_tab&opt_id=10048&opt1_id=9975&opt2_id=999999&opt_g=1&opt_type=3&opt_name=%E7%99%BD%E9%85%92&_x_link_id=c702554fa85a94c0ca877d9f8b19ddb9&_x_goods_id=0&refer_tab=%5B999999%2C%2010048%2C%2010032%2C%2020759%2C%2020758%2C%2020757%2C%2010757%5D&page_from=23&refer_page_name=search&refer_page_id=10031_1756280530115_l1o24l0edg&refer_page_sn=10031"
}

func generateHeaders() http.Header {
	headers := http.Header{}
	headers.Set("Host", "mobile.yangkeduo.com")
	headers.Set("accept", "application/json, text/plain, */*")
	headers.Set("user-agent", buildUserAgent())
	headers.Set("verifyauthtoken", "f5Da7hHLivM1SgmITfAFsQ3a225d976466a0da5")
	headers.Set("sec-fetch-site", "same-origin")
	headers.Set("sec-fetch-mode", "cors")
	headers.Set("sec-fetch-dest", "empty")
	headers.Set("referer", buildReferer())
	headers.Set("accept-language", "zh-CN,zh;q=0.9")
	headers.Set("priority", "u=1, i")
	return headers
}

// 阻塞等待用户在控制台输入新的 PDDAccessToken
func promptNewToken() string {
	reader := bufio.NewReader(os.Stdin)
	for {
		fmt.Print("请输入新的 PDDAccessToken，然后按回车继续采集：\n> ")
		token, _ := reader.ReadString('\n')
		token = strings.TrimSpace(token)
		if token != "" {
			return token
		}
		fmt.Println("输入为空，请重新输入。")
	}
}

// ------------------- 爬虫主体 -------------------

type PinduoduoSpider struct {
	client     *mongo.Client
	collection *mongo.Collection
}

func NewSpider(ctx context.Context) (*PinduoduoSpider, error) {
	// 连接 MongoDB
	clientOpts := options.Client().ApplyURI("**************************************************")
	client, err := mongo.Connect(ctx, clientOpts)
	if err != nil {
		return nil, err
	}
	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		_ = client.Disconnect(ctx)
		return nil, err
	}

	db := client.Database("pinduoduo")
	coll := db.Collection("pinduoduo_baijiu")

	// 创建索引
	_, _ = coll.Indexes().CreateMany(ctx, []mongo.IndexModel{
		{Keys: bson.D{{Key: "goods_id", Value: 1}}, Options: options.Index().SetUnique(true)},
		{Keys: bson.D{{Key: "crawl_time", Value: -1}}},
		{Keys: bson.D{{Key: "page_num", Value: 1}}},
	})

	return &PinduoduoSpider{client: client, collection: coll}, nil
}

func (s *PinduoduoSpider) fetchPageData(ctx context.Context, pageNum int, offset int) (bool, map[string]interface{}) {
	// 复制基础参数
	params := url.Values{}
	for k, v := range baseParams {
		params.Set(k, v)
	}
	params.Set("offset", fmt.Sprintf("%d", offset))

	endpoint := "https://mobile.yangkeduo.com/proxy/api/api/search/opt/10048/groups"
	u, _ := url.Parse(endpoint)
	u.RawQuery = params.Encode()

	client := &http.Client{Timeout: 15 * time.Second}
	for {
		req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
		if err != nil {
			log.Printf("❌ 第 %d 页创建请求失败: %v", pageNum, err)
			return false, nil
		}

		// Cookies
		var cookiePairs []string
		for k, v := range cookies {
			cookiePairs = append(cookiePairs, fmt.Sprintf("%s=%s", k, v))
		}
		req.Header = generateHeaders()
		req.Header.Set("Cookie", strings.Join(cookiePairs, "; "))

		resp, err := client.Do(req)
		if err != nil {
			log.Printf("❌ 第 %d 页请求异常: %v", pageNum, err)
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}

		if resp.StatusCode != 200 {
			log.Printf("❌ 第 %d 页请求失败，状态码: %d", pageNum, resp.StatusCode)
			if resp.Body != nil {
				_ = resp.Body.Close()
			}
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}
		defer resp.Body.Close()

		var data map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&data); err != nil {
			log.Printf("❌ 第 %d 页JSON解析失败: %v", pageNum, err)
			return false, nil
		}

		// 业务层校验
		shouldPrompt := false
		if v, ok := data["error_code"]; ok {
			if int(getNumber(v)) == 54001 {
				shouldPrompt = true
			}
		}
		if strings.ToUpper(getString(data["judgement"])) == "VERIFY" {
			shouldPrompt = true
		}
		if succ, ok := data["success"].(bool); ok && !succ {
			if int(getNumber(data["status"])) == 200 {
				shouldPrompt = true
			}
		}
		if shouldPrompt {
			if tok := getString(data["verify_auth_token"]); tok != "" {
				log.Printf("⚠️ 接口返回校验状态，verify_auth_token=%s", tok)
			}
			newToken := promptNewToken()
			cookies["PDDAccessToken"] = newToken
			continue
		}

		// 打印完整的响应 JSON
		if pretty, err := json.MarshalIndent(data, "", "  "); err == nil {
			log.Printf("📦 第 %d 页响应:\n%s", pageNum, string(pretty))
		}

		log.Printf("✅ 第 %d 页获取成功，offset: %d", pageNum, offset)
		return true, data
	}
}

func (s *PinduoduoSpider) processAndSaveData(ctx context.Context, goodsList []interface{}, pageNum int) int {
	successCount := 0
	now := time.Now()

	for _, it := range goodsList {
		goods, ok := it.(map[string]interface{})
		if !ok {
			continue
		}
		goodsID := getString(goods["goods_id"])
		if goodsID == "" {
			continue
		}

		doc := bson.M{
			"goods_id":                 goodsID,
			"goods_name":               getString(goods["goods_name"]),
			"price":                    getNumber(goods["price"]),
			"price_info":               getString(goods["price_info"]),
			"sales":                    getNumber(goods["sales"]),
			"sales_tip":                getString(goods["sales_tip"]),
			"image_url":                getString(goods["image_url"]),
			"link_url":                 getString(goods["link_url"]),
			"market_price":             getNumber(goods["market_price"]),
			"normal_price":             getNumber(goods["normal_price"]),
			"min_on_sale_group_price":  getNumber(goods["min_on_sale_group_price"]),
			"customer_num":             getNumber(goods["customer_num"]),
			"quality":                  getNumber(goods["quality"]),
			"goods_type":               getNumber(goods["goods_type"]),
			"activity_type":            getNumber(goods["activity_type"]),
			"event_type":               getNumber(goods["event_type"]),
			"crawl_time":               now,
			"page_num":                 pageNum,
			"server_time":              getNumber(goods["server_time"]),
			"raw_data":                 goods,
		}

		filter := bson.M{"goods_id": goodsID}
		update := bson.M{"$set": doc}
		res, err := s.collection.UpdateOne(ctx, filter, update, options.Update().SetUpsert(true))
		if err != nil {
			log.Printf("     商品 %s 保存失败: %v", goodsID, err)
			continue
		}
		if res.UpsertedID != nil || res.ModifiedCount > 0 || res.MatchedCount > 0 {
			successCount++
		}
	}
	return successCount
}

func (s *PinduoduoSpider) run(ctx context.Context, totalPages int) {
	countPerPage := 20
	successfulPages := 0
	failedPages := 0
	totalGoods := 0

	log.Println("🚀 开始爬取拼多多白酒商品数据并保存到数据库...")

	start := loadProgressBaijiu() + 1
	if start < 1 {
		start = 1
	}
	for pageNum := start; pageNum <= totalPages; pageNum++ {
		offset := (pageNum - 1) * countPerPage
		log.Printf("\n正在获取第 %d 页数据...", pageNum)

		success, data := s.fetchPageData(ctx, pageNum, offset)
		if success {
			successfulPages++
			if data != nil {
				if gl, ok := data["goods_list"].([]interface{}); ok {
					cnt := s.processAndSaveData(ctx, gl, pageNum)
					saveProgressBaijiu(pageNum)
					totalGoods += cnt
				}
			}
		} else {
			failedPages++
		}

		// 每20页暂停20分钟
		if pageNum%20 == 0 && pageNum < totalPages {
			log.Printf("\n🛑 已完成 %d 页，暂停 20 分钟休息...", pageNum)
			log.Printf("⏰ 暂停开始时间: %s", time.Now().Format("2006-01-02 15:04:05"))
			restDuration := 20 * time.Minute
			time.Sleep(restDuration)
			log.Printf("🚀 休息结束，继续采集... 当前时间: %s", time.Now().Format("2006-01-02 15:04:05"))
		} else if pageNum < totalPages {
			delay := 15 + rand.Float64()*10
			log.Printf("⏳ 等待 %.1f 秒后继续...", delay)
			time.Sleep(time.Duration(delay * float64(time.Second)))
		}

		if pageNum%10 == 0 {
			log.Printf("\n📊 进度: %d/%d页", pageNum, totalPages)
			log.Printf("✅ 成功: %d页, ❌ 失败: %d页", successfulPages, failedPages)
			log.Printf("📦 总商品数(本次新/更): %d", totalGoods)
			log.Println(strings.Repeat("-", 50))
		}
	}

	log.Println("\n🎉 爬取完成!")
	log.Printf("总页数: %d", totalPages)
	log.Printf("成功页数: %d", successfulPages)
	log.Printf("失败页数: %d", failedPages)
	log.Printf("总商品数(本次新/更): %d", totalGoods)
}

func (s *PinduoduoSpider) getStats(ctx context.Context) {
	total, err := s.collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		log.Printf("统计失败: %v", err)
		return
	}
	var latest bson.M
	err = s.collection.FindOne(ctx, bson.M{}, options.FindOne().SetSort(bson.D{{Key: "crawl_time", Value: -1}})).Decode(&latest)
	log.Printf("\n📊 数据库统计:")
	log.Printf("总记录数: %d", total)
	if err == nil {
		log.Printf("最新采集时间: %v", latest["crawl_time"])
		log.Printf("最新商品ID: %v", latest["goods_id"])
	}
}

// ------------------- 工具函数 -------------------

func getString(v interface{}) string {
	switch t := v.(type) {
	case string:
		return t
	case json.Number:
		return t.String()
	case float64:
		return fmt.Sprintf("%v", t)
	case int64:
		return fmt.Sprintf("%d", t)
	default:
		return ""
	}
}

func getNumber(v interface{}) float64 {
	switch t := v.(type) {
	case float64:
		return t
	case int:
		return float64(t)
	case int64:
		return float64(t)
	case json.Number:
		f, _ := t.Float64()
		return f
	case string:
		if f, err := strconv.ParseFloat(t, 64); err == nil {
			return f
		}
	}
	return 0
}

// ------------------- 入口 -------------------

func main() {
	rand.Seed(time.Now().UnixNano())
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	spider, err := NewSpider(ctx)
	if err != nil {
		log.Fatalf("创建爬虫失败: %v", err)
	}
	defer func() {
		_ = spider.client.Disconnect(context.Background())
	}()

	spider.run(context.Background(), 100)
	spider.getStats(context.Background())
}