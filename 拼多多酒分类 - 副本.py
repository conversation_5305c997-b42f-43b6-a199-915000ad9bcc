import requests
import time
import random
import json
import pymongo
from datetime import datetime
from urllib.parse import unquote

cookies = {
    'api_uid': 'CiaZOGiuseGoRQCIjIsKAg==',
    'PDDAccessToken': '5GXKZE6ZCEARBHCZXIBHIPHL7GGYFGZMIAZ2HYE6MDVR37XMAMKQ1202f09',
    'pdd_user_id': '1582604126255',
    'pdd_user_uin': '6OAAWPMH5TADRC5ICVSA2JDX4I_GEXDA',
    '_nano_fp': 'Xpmyn5P8lpPjn5TxX9_U7kegGW3dHyG2lVm~KCAT',
    'webp': '1',
    'jrpl': 'pCHZ6uFydCAYdROu2dmzW85B70WW05QA',
    'njrpl': 'pCHZ6uFydCAYdROu2dmzW85B70WW05QA',
    'dilx': 'lCP8342P7nobLSKUn8rH_',
    'pdd_vds': 'gaYLCyTPuilPNEcQTasPdONiTicNToBmTtDmlGctlIuQubTLfICEebCtCtsn',
}

# -------- 动态 Header 生成：尽量只随机“可安全变更”的字段，保留易校验字段不动 --------
ANDROID_DEVICES = [
    'Redmi Note 8', 'Mi 10', 'HUAWEI P40', 'OPPO R11', 'vivo X27', 'OnePlus 8 Pro',
    'HONOR V30', 'Meizu 16th', 'Samsung SM-G9730', 'Pixel 4 XL'
]
ANDROID_VERSIONS = ['10', '11', '12', '13']
NET_TYPES = ['WIFI', '4G', '5G']


def _random_hex8():
    return ''.join(random.choice('0123456789ABCDEF') for _ in range(8))


def _random_chrome_version():
    # 包含当前示例的 138，也包含常见版本号范围
    major = random.choice([120, 121, 122, 123, 124, 125, 126, 138])
    build1 = random.randint(3000, 9999)
    build2 = random.randint(50, 199)
    return f"{major}.0.{build1}.{build2}", major


def _random_wechat_sdk_date():
    # 近 90 天内任意日期，格式类似 20250503
    days_ago = random.randint(0, 90)
    ts = time.time() - days_ago * 86400
    return time.strftime('%Y%m%d', time.localtime(ts))


def _random_xweb_version():
    # 模拟 XWEB/{major}{minor}{build} 的样式
    major = random.randint(135, 139)
    minor = random.randint(0, 9)
    build = random.randint(100, 999)
    return f"{major}{minor}{build}"


def _random_wx_version():
    # 形如 8.0.61.2880(0x28003D34)
    patch = random.randint(58, 66)
    build = random.randint(2600, 3200)
    hexpart = _random_hex8()
    return f"8.0.{patch}.{build}(0x{hexpart})"


def _build_user_agent():
    chrome_ver, chrome_major = _random_chrome_version()
    device = random.choice(ANDROID_DEVICES)
    android_ver = random.choice(ANDROID_VERSIONS)
    xweb = _random_xweb_version()
    sdk_date = _random_wechat_sdk_date()
    mwebid = random.randint(1000, 9999)
    wx_ver = _random_wx_version()
    net = random.choice(NET_TYPES)
    # 保持与原有格式尽量一致
    ua = (
        f"Mozilla/5.0 (Linux; Android {android_ver}; {device} Build/RKQ1.201004.002; wv) "
        f"AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 "
        f"Chrome/{chrome_ver} Mobile Safari/537.36 "
        f"XWEB/{xweb} MMWEBSDK/{sdk_date} MMWEBID/{mwebid} "
        f"MicroMessenger/{wx_ver} WeChat/arm64 Weixin NetType/{net} "
        f"Language/zh_CN ABI/arm64"
    )
    return ua, chrome_major


def _build_referer(opt_id='109', opt_name_encoded='%E4%B8%AD%E5%A4%96%E5%90%8D%E9%85%92', opt_type='2', goods_id='757482183038'):
    # 动态化少量 query 字段（一般可安全变更，不影响接口签名）
    x_share_id = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=22))
    suffix = ''.join(random.choices('abcdefghijklmnopqrstuvwxyz0123456789', k=10))
    refer_page_id = f"10002_{int(time.time()*1000)}_{suffix}"
    return (
        "https://mobile.pinduoduo.com/catgoods.html?refer_page_name=index"
        f"&opt_id={opt_id}&opt_name={opt_name_encoded}&opt_type={opt_type}"
        f"&goods_id={goods_id}&_x_share_id={x_share_id}&refer_page_id={refer_page_id}&refer_page_sn=10002"
    )


def generate_headers():
    # 使用这次提供的新接口所需的固定头
    return {
        'Host': 'mobile.yangkeduo.com',
        'accept': 'application/json, text/plain, */*',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x6309092b) XWEB/14315 Flue',
        'verifyauthtoken': 'gvvTj2im23NmAqzIDERGAA7990c15ebed0ea0ca',
        'sec-fetch-site': 'same-origin',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'referer': 'https://mobile.yangkeduo.com/relative_goods.html?__rp_name=search_catgoods_tab&opt_id=10048&opt1_id=9975&opt2_id=999999&opt_g=1&opt_type=3&opt_name=%E7%99%BD%E9%85%92&_x_link_id=25b1f459f36f02d89bc658e14dbd943f&_x_goods_id=0&refer_tab=%5B999999%2C%2010048%2C%2010016%2C%2010006%2C%209997%2C%2024112%2C%2020759%5D&page_from=23&refer_page_name=search&refer_page_id=10031_1756279304636_ruj3jfjsya&refer_page_sn=10031',
        'accept-language': 'zh-CN,zh;q=0.9',
        'priority': 'u=1, i',
    }

# 新分类接口基础参数模板（除分页外尽量固定按您提供的示例）
base_search_params = {
    'pdduid': '1582604126255',
    'source': 'search',
    'opt_type': '3',
    'white_ground_pic_goods_id': '0',
    'refer_tab': '[999999,+10048,+10016,+10006,+9997,+24112,+20759]',
    'opt1_id': '9975',
    'opt2_id': '999999',
    'opt_name': '白酒',
    '_x_link_id': '25b1f459f36f02d89bc658e14dbd943f',
    'flip': '0;0;0;0;f2ad23c0-35e3-eb9d-e69f-c21aba13077c;/20;0;0;4ddaf5a5cd255c2e2e9bb46eb22a6e85',
    'offset': '0',
    'count': '20',
    'list_id': 'mw3vy5fccu',
    'sort_type': 'default',
    'filter': '',
    'tab_enable': '1',
    'anti_content': '0asWfqnydiPssgu2z2iNL9Lj0yPeo2T4HEYmRCQkFo1khh5HMcnHEvkkMWCOyiSAIDA8ETrZg7FPvsf51BeIWhC05dm5n4x0SdPVqUOg6dwq-n4Zn0CnjcAKYCaQoYMCwzYeJbd49pVanQA6qUUaZTPHsQ70uPSIouhbTuj24HgTpVtSZ2oa9Sa5UV41NgDuw_7aC5XrqIbEzdWcvfjZEKJbRC7ITJ5pmZfE_q0a04N0PWfjRgjlg7N4FAVZpVgmN66x4ca6GbORiRmVBog9EV6C5sWV1QaN5aPzt_MbwYxlj7BqNwoj2xNwcaFo5sR1hmZjjMzzTvbWN3J6U3Dif803TKPm22CgJuiI6-e9eOjpzYcmTdKz_0SWe3rVBgOlbnns4x1WDX6kec8dGuc1K2XYunSdAIarDamY6M7W_5AGUU1oBDZ5PhyV13GL31EQd3r7MDQrEZ1SPjaG2c_zPGYIJ2eso84zjJbIxKyPcqWan4FOQvazzmsGu8r2dEMTyfeeI7TKo4OO1RpweMEabtKtPtdrXiyGU-r08V-E0IYHWN3DGQQeAQdB06PyHd1SXzDo_LAdBMl0mGjn6FZZWH1Q5-dwYubUK-_tnl4Qded3t9VymdcY71mLYrDx08PU5BixLLUKrM4m_BBbLdVLYzyuDjGtR4zvxGPdyOle-GxKJBL0WOsha83GOKmIX0PfXRYTd37hsI8p3fk-VOfUEEySrk__Xi1SSN5OBg8YBCILX3ddqn0T1xP6Y7GIGR8yrstWpB1MgLqlqRICkJZi9M32xMx-f2cGOvbNLwjo5EDK9rRAzPLRNz1LImMhHVGFas5ljRArcVlp-wb5T8wCN4GbhpltnklcNAayddxRjZ6nnbAAjODUgZF9_q2OEEUbrE-Vbk8IUvDd1nsm4eppEI6IHSfm6O4IjZQI6DU1E16G0lMW8DN8s6XItjAN7VpqF_iIlCMEaw7aEhtRV7SC_NI-UWFN9z',
    'opt_source': 'search_opt_goods',
}


class PinduoduoSpider:
    def __init__(self):
        # MongoDB连接
        self.client = pymongo.MongoClient(
            "**************************************************",
            compressors='zstd'
        )
        # 创建拼多多数据库和数据表
        self.db = self.client["pinduoduo"]
        self.collection = self.db["pinduoduo_jiu"]

        # 创建索引，确保goods_id唯一性
        self.collection.create_index("goods_id", unique=True)
        self.collection.create_index("crawl_time")
        self.collection.create_index("page_num")

    def fetch_page_data(self, page_num, offset):
        """
        使用新的搜索接口获取指定页码的数据（page 参数），忽略 offset
        """
        params = base_search_params.copy()
        params['offset'] = str(offset)

        try:
            # 发送请求到新的分类接口
            response = requests.get(
                'https://mobile.yangkeduo.com/proxy/api/api/search/opt/10048/groups',
                params=params,
                cookies=cookies,
                headers=generate_headers(),
                timeout=15
            )

            # 检查请求是否成功
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ 第 {page_num} 页获取成功，page: {page_num}")

                    # 提取商品列表（兼容多种返回结构）
                    goods_list = self.extract_goods_list(data)
                    print(f"   数据条数: {len(goods_list)}")

                    # 处理并保存数据到数据库
                    success_count = self.process_and_save_data(goods_list, page_num)
                    print(f"   成功保存 {success_count} 条数据到数据库")

                    return True, data
                except json.JSONDecodeError:
                    print(f"❌ 第 {page_num} 页JSON解析失败")
                    return False, None
            else:
                print(f"❌ 第 {page_num} 页请求失败，状态码: {response.status_code}")
                return False, None

        except requests.exceptions.RequestException as e:
            print(f"❌ 第 {page_num} 页请求异常: {e}")
            return False, None
        except Exception as e:
            print(f"❌ 第 {page_num} 页处理异常: {e}")
            return False, None

    def process_and_save_data(self, goods_list, page_num):
        """
        处理商品数据并保存到数据库
        """
        success_count = 0
        current_time = datetime.now()

        for goods_data in goods_list:
            try:
                # 提取goods_id
                goods_id = goods_data.get('goods_id')
                if not goods_id:
                    continue

                # 准备要保存的数据（字段已在 extract_goods_list 中做过归一化）
                document = {
                    'goods_id': goods_id,
                    'goods_name': goods_data.get('goods_name', ''),
                    'price': goods_data.get('price', 0),
                    'price_info': goods_data.get('price_info', ''),
                    'sales': goods_data.get('sales', 0),
                    'sales_tip': goods_data.get('sales_tip', ''),
                    'image_url': goods_data.get('image_url', ''),
                    'link_url': goods_data.get('link_url', ''),
                    'market_price': goods_data.get('market_price', 0),
                    'normal_price': goods_data.get('normal_price', 0),
                    'min_on_sale_group_price': goods_data.get('min_on_sale_group_price', 0),
                    'customer_num': goods_data.get('customer_num', 0),
                    'quality': goods_data.get('quality', 0),
                    'goods_type': goods_data.get('goods_type', 0),
                    'activity_type': goods_data.get('activity_type', 0),
                    'event_type': goods_data.get('event_type', 0),

                    # 添加时间信息
                    'crawl_time': current_time,
                    'page_num': page_num,
                    'server_time': goods_data.get('server_time') if 'server_time' in goods_data else None,

                    # 原始数据（可选，如果需要完整数据可以保存）
                    'raw_data': goods_data.get('raw_data', goods_data)
                }

                # 尝试插入数据，如果已存在则更新
                result = self.collection.update_one(
                    {'goods_id': goods_id},
                    {'$set': document},
                    upsert=True
                )

                if result.upserted_id or result.modified_count > 0:
                    success_count += 1
                    # print(f"     商品 {goods_id} 保存成功")
                else:
                    print(f"     商品 {goods_id} 已存在，无需更新")

            except Exception as e:
                print(f"     商品数据处理失败: {e}")
                continue

        return success_count

    def extract_goods_list(self, data):
        """
        从新接口返回的数据中提取商品列表，并做字段归一化
        """
        if not isinstance(data, dict):
            return []

        candidates = []
        # 先尝试常见键
        for key in ['goods_list', 'items', 'list', 'result', 'data']:
            v = data.get(key)
            if isinstance(v, list):
                candidates = v
                break
            if isinstance(v, dict):
                for k2 in ['goods_list', 'items', 'list', 'result']:
                    v2 = v.get(k2)
                    if isinstance(v2, list):
                        candidates = v2
                        break
                if candidates:
                    break

        # 递归兜底查找列表
        if not candidates:
            def find_list(obj):
                if isinstance(obj, list) and obj and isinstance(obj[0], dict):
                    return obj
                if isinstance(obj, dict):
                    for vv in obj.values():
                        found = find_list(vv)
                        if found:
                            return found
                return None
            found = find_list(data)
            if isinstance(found, list):
                candidates = found

        # 归一化
        normalized = []
        for item in candidates:
            if not isinstance(item, dict):
                continue
            normalized.append(self._normalize_item(item))
        # 过滤没有 goods_id 的
        normalized = [it for it in normalized if it.get('goods_id')]
        return normalized

    def _normalize_item(self, item: dict) -> dict:
        """
        将不同字段名映射为统一字段，便于后续入库复用
        """
        gid = item.get('goods_id') or item.get('goodsId') or item.get('id') or item.get('id_str')
        if gid is not None:
            try:
                gid = str(gid)
            except Exception:
                pass

        name = item.get('goods_name') or item.get('goodsName') or item.get('name') or item.get('title', '')
        price = (item.get('price') or item.get('min_group_price') or item.get('group_price')
                 or item.get('normal_price') or 0)
        sales = item.get('sales') or item.get('cnt') or item.get('sale') or 0
        sales_tip = item.get('sales_tip') or item.get('salesTip') or ''
        image_url = (item.get('image_url') or item.get('hd_thumb_url') or item.get('thumb_url')
                     or item.get('image') or '')
        link_url = item.get('link_url') or item.get('linkUrl') or ''
        market_price = item.get('market_price') or item.get('marketPrice') or 0
        normal_price = item.get('normal_price') or item.get('normalPrice') or 0
        min_on_sale_group_price = item.get('min_on_sale_group_price') or item.get('min_group_price') or 0
        customer_num = item.get('customer_num') or 0
        quality = item.get('quality') or 0
        goods_type = item.get('goods_type') or 0
        activity_type = item.get('activity_type') or 0
        event_type = item.get('event_type') or 0

        return {
            'goods_id': gid,
            'goods_name': name,
            'price': price,
            'price_info': item.get('price_info', ''),
            'sales': sales,
            'sales_tip': sales_tip,
            'image_url': image_url,
            'link_url': link_url,
            'market_price': market_price,
            'normal_price': normal_price,
            'min_on_sale_group_price': min_on_sale_group_price,
            'customer_num': customer_num,
            'quality': quality,
            'goods_type': goods_type,
            'activity_type': activity_type,
            'event_type': event_type,
            'server_time': item.get('server_time'),
            'raw_data': item,
        }

    def run(self, total_pages=100):
        """
        运行爬虫
        """
        count_per_page = 20
        successful_pages = 0
        failed_pages = 0
        total_goods_count = 0

        print("🚀 开始爬取拼多多商品数据并保存到数据库...")

        for page_num in range(1, total_pages + 1):
            # 计算偏移量：offset = (页码 - 1) * 每页数量
            offset = (page_num - 1) * count_per_page

            print(f"\n正在获取第 {page_num} 页数据...")

            # 获取数据
            success, data = self.fetch_page_data(page_num, offset)

            if success:
                successful_pages += 1
                # 统计本页商品数量（从新接口解析）
                if data:
                    total_goods_count += len(self.extract_goods_list(data))
            else:
                failed_pages += 1

            # 添加随机延迟，避免请求过于频繁被封IP
            if page_num < total_pages:
                delay = random.uniform(15, 20)
                print(f"⏳ 等待 {delay:.1f} 秒后继续...")
                time.sleep(delay)

            # 每10页打印一次进度
            if page_num % 10 == 0:
                print(f"\n📊 进度: {page_num}/{total_pages}页")
                print(f"✅ 成功: {successful_pages}页, ❌ 失败: {failed_pages}页")
                print(f"📦 总商品数: {total_goods_count}")
                print("-" * 50)

        print(f"\n🎉 爬取完成!")
        print(f"总页数: {total_pages}")
        print(f"成功页数: {successful_pages}")
        print(f"失败页数: {failed_pages}")
        print(f"总商品数: {total_goods_count}")

        # 关闭数据库连接
        self.client.close()

    def get_stats(self):
        """
        获取数据库统计信息
        """
        total_count = self.collection.count_documents({})
        latest_record = self.collection.find_one(sort=[('crawl_time', pymongo.DESCENDING)])

        print(f"\n📊 数据库统计:")
        print(f"总记录数: {total_count}")
        if latest_record:
            print(f"最新采集时间: {latest_record['crawl_time']}")
            print(f"最新商品ID: {latest_record['goods_id']}")

if __name__ == "__main__":
    # 创建爬虫实例并运行
    spider = PinduoduoSpider()

    try:
        # 运行爬虫，爬取100页数据
        spider.run(total_pages=100)

        # 显示统计信息
        spider.get_stats()

    except KeyboardInterrupt:
        print("\n⏹️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")
    finally:
        # 确保数据库连接关闭
        if hasattr(spider, 'client'):
            spider.client.close()
        print("程序结束")

